# Video Scene Detection API

## Overview

The Scene Detection API provides functionality to automatically detect scene changes within videos and return timestamps for the start and end of each scene. This document outlines the implementation details and usage guidelines for this feature.

## Implementation

### 1. Technical Approach

The API uses FFmpeg's scene detection capabilities via the `select` filter with `scene` detection. This approach analyzes frame-to-frame differences to identify significant visual changes that likely represent scene transitions.

### 2. Backend Implementation

#### 2.1 API Endpoints

```
POST /api/scene-detection
GET /api/scene-detection/:taskId
```

#### 2.2 Request Format (POST)

```json
{
  "videoPath": "path/to/video.mp4",
  "threshold": 0.4,           // Optional: Scene change threshold (0.1-1.0, default: 0.4)
  "minSceneDuration": 1.0     // Optional: Minimum scene duration in seconds (default: 1.0)
}
```

#### 2.3 Response Format (POST)

```json
{
  "taskId": "1627381927382",
  "status": "processing",
  "message": "Scene detection started"
}
```

#### 2.4 Response Format (GET)

```json
{
  "taskId": "1627381927382",
  "status": "completed",
  "progress": 100,
  "scenes": [
    {
      "index": 0,
      "startTime": 0,
      "endTime": 12.45,
      "startTimeFormatted": "00:00:00",
      "endTimeFormatted": "00:00:12.45",
      "durationSeconds": 12.45,
      "thumbnailUrl": "/thumbnails/task_1627381927382/scene_0.jpg"
    },
    {
      "index": 1,
      "startTime": 12.45,
      "endTime": 25.78,
      "startTimeFormatted": "00:00:12.45",
      "endTimeFormatted": "00:00:25.78",
      "durationSeconds": 13.33,
      "thumbnailUrl": "/thumbnails/task_1627381927382/scene_1.jpg"
    },
    // Additional scenes...
  ]
}
```

### 3. Implementation Details

#### 3.1 Scene Detection Controller

Create a `SceneDetectionController.ts` file in the controllers directory:

```typescript
import { Request, Response } from "express";
import { spawn } from "child_process";
import fs from "fs";
import path from "path";
import os from "os";
import { ProgressTracker } from "../services/ProgressTracker";
import { TaskQueue } from "../services/TaskQueue";
import logger from "../utils/logger";

export class SceneDetectionController {
  private progressTracker: ProgressTracker;
  private taskQueue: TaskQueue;

  constructor(progressTracker: ProgressTracker, taskQueue: TaskQueue) {
    this.progressTracker = progressTracker;
    this.taskQueue = taskQueue;
  }

  async detectScenes(req: Request, res: Response): Promise<void> {
    const { videoPath, threshold = 0.4, minSceneDuration = 1.0 } = req.body;
    const taskId = Date.now().toString();

    if (!videoPath) {
      res.status(400).json({ error: "videoPath is required" });
      return;
    }

    try {
      // Initialize task progress
      this.progressTracker.initializeTask(taskId);

      // Add task to queue with high priority
      const queuePosition = this.taskQueue.enqueue(
        taskId,
        async () => {
          await this.executeSceneDetectionTask(taskId, videoPath, threshold, minSceneDuration);
        },
        { isSceneDetection: true }
      );

      // Return task ID and queue position
      res.json({
        taskId,
        queuePosition,
        status: queuePosition > 1 ? "queued" : "processing",
        message:
          queuePosition > 1
            ? `Scene detection queued, position: ${queuePosition}`
            : "Scene detection started"
      });
    } catch (error) {
      logger.error(`Scene detection task ${taskId} creation failed:`, error);
      this.markTaskFailed(
        taskId,
        error instanceof Error ? error.message : "Unknown error"
      );

      if (!res.headersSent) {
        res.status(500).json({
          error: "Internal server error",
          details: error instanceof Error ? error.message : "Unknown error"
        });
      }
    }
  }

  async getSceneDetectionStatus(req: Request, res: Response): Promise<void> {
    const taskId = req.params.taskId;
    const progress = this.progressTracker.getProgress(taskId);

    if (!progress) {
      // Check if task is queued but not yet started
      const taskInfo = this.taskQueue.getTaskInfo(taskId);
      if (taskInfo) {
        const queuePosition = this.taskQueue.getQueuePosition(taskId);
        res.json({
          taskId,
          status: "queued",
          progress: 0,
          queuePosition,
          message: `Scene detection queued, position: ${queuePosition}`
        });
        return;
      }
      res.status(404).json({ error: "Task not found" });
      return;
    }

    res.json({
      ...progress,
      taskId
    });
  }

  private async executeSceneDetectionTask(
    taskId: string,
    videoPath: string,
    threshold: number,
    minSceneDuration: number
  ): Promise<void> {
    let ffmpeg: ReturnType<typeof spawn> | null = null;
    let timeout: NodeJS.Timeout | null = null;
    const outputDir = path.join(os.tmpdir(), `scene_detection_${taskId}`);
    const sceneDataPath = path.join(outputDir, "scenes.txt");
    const thumbnailsDir = path.join("server/public/thumbnails", `task_${taskId}`);

    try {
      // Create output directories
      fs.mkdirSync(outputDir, { recursive: true });
      fs.mkdirSync(thumbnailsDir, { recursive: true });

      logger.info(`Starting scene detection for ${videoPath} with threshold ${threshold}`);
      this.progressTracker.updateTaskProgress(taskId, {
        status: "processing",
        progress: 5,
        stage: "initializing"
      });

      // First pass: Detect scenes and output timestamps
      const scenes = await this.detectScenesWithFFmpeg(taskId, videoPath, threshold, sceneDataPath);
      
      // Filter scenes by minimum duration
      const filteredScenes = this.filterScenesByDuration(scenes, minSceneDuration);
      
      // Generate thumbnails for each scene
      const scenesWithThumbnails = await this.generateSceneThumbnails(
        taskId, 
        videoPath, 
        filteredScenes,
        thumbnailsDir
      );

      // Mark task as complete with scene data
      this.progressTracker.updateTaskProgress(taskId, {
        status: "completed",
        progress: 100,
        stage: "completed",
        scenes: scenesWithThumbnails
      });

      logger.info(`Scene detection for task ${taskId} completed successfully with ${filteredScenes.length} scenes`);
    } catch (error) {
      logger.error(`Scene detection task ${taskId} failed:`, error);
      this.markTaskFailed(
        taskId,
        error instanceof Error ? error.message : "Unknown error"
      );
      throw error;
    } finally {
      // Clean up temporary files
      try {
        if (fs.existsSync(sceneDataPath)) {
          fs.unlinkSync(sceneDataPath);
        }
        if (fs.existsSync(outputDir)) {
          fs.rmdirSync(outputDir, { recursive: true });
        }
      } catch (err) {
        logger.error(`Failed to clean up temporary files for task ${taskId}:`, err);
      }
    }
  }

  private async detectScenesWithFFmpeg(
    taskId: string,
    videoPath: string,
    threshold: number,
    outputPath: string
  ): Promise<any[]> {
    return new Promise((resolve, reject) => {
      // Update progress
      this.progressTracker.updateTaskProgress(taskId, {
        status: "processing",
        progress: 10,
        stage: "detecting scenes"
      });

      // FFmpeg command for scene detection
      const cmd = "ffmpeg";
      const args = [
        "-i", videoPath,
        "-vf", `select='gt(scene,${threshold})',metadata=print:file='${outputPath}'`,
        "-f", "null", "-"
      ];

      const ffmpeg = spawn(cmd, args);
      
      // Track progress
      let lastProgress = 10;
      ffmpeg.stderr.on("data", (data) => {
        const line = data.toString();
        
        // Try to extract progress information
        const timeMatch = line.match(/time=(\d{2}):(\d{2}):(\d{2})\.(\d{2})/);
        const durationMatch = line.match(/Duration: (\d{2}):(\d{2}):(\d{2})\.(\d{2})/);
        
        if (timeMatch && durationMatch) {
          const currentTime = 
            parseInt(timeMatch[1]) * 3600 + 
            parseInt(timeMatch[2]) * 60 + 
            parseInt(timeMatch[3]) + 
            parseInt(timeMatch[4]) / 100;
          
          const totalDuration = 
            parseInt(durationMatch[1]) * 3600 + 
            parseInt(durationMatch[2]) * 60 + 
            parseInt(durationMatch[3]) + 
            parseInt(durationMatch[4]) / 100;
          
          const progressPercent = Math.min(85, 10 + Math.floor((currentTime / totalDuration) * 75));
          
          if (progressPercent > lastProgress) {
            lastProgress = progressPercent;
            this.progressTracker.updateTaskProgress(taskId, {
              status: "processing",
              progress: progressPercent,
              stage: "detecting scenes"
            });
          }
        }
      });

      // Handle process completion
      ffmpeg.on("error", reject);
      
      ffmpeg.on("close", async (code) => {
        if (code === 0) {
          try {
            // Parse scene data
            const scenes = await this.parseSceneDetectionOutput(outputPath, videoPath);
            resolve(scenes);
          } catch (err) {
            reject(err);
          }
        } else {
          reject(new Error(`FFmpeg process exited with code ${code}`));
        }
      });
    });
  }

  private async parseSceneDetectionOutput(filePath: string, videoPath: string): Promise<any[]> {
    // Check if output file exists
    if (!fs.existsSync(filePath)) {
      return [];
    }
    
    // Read the output file
    const content = fs.readFileSync(filePath, "utf-8");
    const lines = content.split("\n");
    
    // Get video duration using FFprobe
    const videoDuration = await this.getVideoDuration(videoPath);
    
    // Process scene detection data
    const sceneChanges: number[] = [];
    
    for (const line of lines) {
      if (line.includes("lavfi.scene_score=")) {
        // Extract timestamp from line
        const timeMatch = line.match(/pts_time:(\d+\.\d+)/);
        if (timeMatch && timeMatch[1]) {
          const timestamp = parseFloat(timeMatch[1]);
          sceneChanges.push(timestamp);
        }
      }
    }
    
    // Sort timestamps and ensure we start from 0
    sceneChanges.sort((a, b) => a - b);
    if (sceneChanges.length === 0 || sceneChanges[0] > 0.1) {
      sceneChanges.unshift(0);
    }
    
    // Ensure the last scene ends at video duration
    if (videoDuration && (sceneChanges.length === 0 || sceneChanges[sceneChanges.length - 1] < videoDuration - 0.5)) {
      sceneChanges.push(videoDuration);
    }
    
    // Create scene objects with start and end times
    const scenes = [];
    for (let i = 0; i < sceneChanges.length - 1; i++) {
      const startTime = sceneChanges[i];
      const endTime = sceneChanges[i + 1];
      
      scenes.push({
        index: i,
        startTime,
        endTime,
        startTimeFormatted: this.formatTimeStamp(startTime),
        endTimeFormatted: this.formatTimeStamp(endTime),
        durationSeconds: endTime - startTime
      });
    }
    
    return scenes;
  }

  private async getVideoDuration(videoPath: string): Promise<number> {
    return new Promise((resolve, reject) => {
      const ffprobe = spawn("ffprobe", [
        "-v", "error",
        "-show_entries", "format=duration",
        "-of", "default=noprint_wrappers=1:nokey=1",
        videoPath
      ]);
      
      let output = "";
      ffprobe.stdout.on("data", (data) => {
        output += data.toString();
      });
      
      ffprobe.on("error", reject);
      
      ffprobe.on("close", (code) => {
        if (code === 0) {
          try {
            const duration = parseFloat(output.trim());
            resolve(duration);
          } catch (err) {
            reject(new Error("Failed to parse video duration"));
          }
        } else {
          reject(new Error(`FFprobe exited with code ${code}`));
        }
      });
    });
  }

  private formatTimeStamp(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${remainingSeconds.toFixed(2).padStart(5, "0")}`;
  }

  private filterScenesByDuration(scenes: any[], minDuration: number): any[] {
    return scenes.filter(scene => scene.durationSeconds >= minDuration);
  }

  private async generateSceneThumbnails(
    taskId: string,
    videoPath: string,
    scenes: any[],
    thumbnailsDir: string
  ): Promise<any[]> {
    // Update progress
    this.progressTracker.updateTaskProgress(taskId, {
      status: "processing",
      progress: 85,
      stage: "generating thumbnails"
    });
    
    const scenesWithThumbnails = [...scenes];
    const thumbnailPromises = scenes.map(async (scene, index) => {
      const thumbnailPath = path.join(thumbnailsDir, `scene_${index}.jpg`);
      const relativePath = `/thumbnails/task_${taskId}/scene_${index}.jpg`;
      
      // Calculate thumbnail position (1 second into the scene or middle if shorter)
      const thumbnailPosition = Math.min(
        scene.startTime + 1, 
        scene.startTime + (scene.durationSeconds / 2)
      );
      
      try {
        await this.extractThumbnail(videoPath, thumbnailPosition, thumbnailPath);
        scenesWithThumbnails[index] = {
          ...scene,
          thumbnailUrl: relativePath
        };
      } catch (err) {
        logger.error(`Failed to generate thumbnail for scene ${index} of task ${taskId}:`, err);
        scenesWithThumbnails[index] = {
          ...scene,
          thumbnailUrl: null
        };
      }
      
      // Update progress (distribute from 85% to 95%)
      const progressIncrement = 10 / scenes.length;
      this.progressTracker.updateTaskProgress(taskId, {
        status: "processing",
        progress: Math.min(95, 85 + Math.floor((index + 1) * progressIncrement)),
        stage: "generating thumbnails"
      });
    });
    
    await Promise.all(thumbnailPromises);
    return scenesWithThumbnails;
  }

  private async extractThumbnail(
    videoPath: string,
    timePosition: number,
    outputPath: string
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const cmd = "ffmpeg";
      const args = [
        "-ss", timePosition.toString(),
        "-i", videoPath,
        "-vframes", "1",
        "-q:v", "2",
        "-y",
        outputPath
      ];
      
      const ffmpeg = spawn(cmd, args);
      
      ffmpeg.on("error", reject);
      
      ffmpeg.on("close", (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Thumbnail extraction process exited with code ${code}`));
        }
      });
    });
  }

  private markTaskFailed(taskId: string, errorMessage: string): void {
    const currentProgress = this.progressTracker.getProgress(taskId);
    if (currentProgress) {
      this.progressTracker.initializeTask(taskId);
      const progress = this.progressTracker.getProgress(taskId);
      if (progress) {
        progress.status = "failed";
        progress.stage = "failed";
        progress.error = errorMessage;
        progress.progress = 0;
      }
    }
  }
}
```

#### 3.2 Add to Routes

Add the following routes to your Express application:

```typescript
// In routes.ts or similar
import { SceneDetectionController } from "./controllers/SceneDetectionController";

// Initialize controller
const sceneDetectionController = new SceneDetectionController(progressTracker, taskQueue);

// Add routes
router.post("/api/scene-detection", sceneDetectionController.detectScenes.bind(sceneDetectionController));
router.get("/api/scene-detection/:taskId", sceneDetectionController.getSceneDetectionStatus.bind(sceneDetectionController));
```

#### 3.3 Update ProgressInfo Interface

Extend the `ProgressInfo` interface in `ProgressTracker.ts` to include scene data:

```typescript
export interface ProgressInfo {
  status: "pending" | "processing" | "completed" | "failed" | "cancelled";
  progress: number;
  currentFrame: number | null;
  totalFrames: number | null;
  fps: number | null;
  speed: number | null;
  error?: string;
  lastUpdate: number;
  stage?: string;
  downloadUrl?: string;
  scenes?: any[]; // Add this line for scene data
}
```

## Usage Examples

### Requesting Scene Detection

```javascript
// Example client-side code
async function detectScenes(videoPath) {
  try {
    const response = await fetch('/api/scene-detection', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        videoPath,
        threshold: 0.4,          // Optional
        minSceneDuration: 1.5    // Optional
      }),
    });
    
    const data = await response.json();
    return data.taskId;
  } catch (error) {
    console.error('Scene detection request failed:', error);
    throw error;
  }
}
```

### Polling for Results

```javascript
// Example client-side code
async function pollSceneDetectionStatus(taskId) {
  try {
    const response = await fetch(`/api/scene-detection/${taskId}`);
    const data = await response.json();
    
    if (data.status === 'completed') {
      // Process completed scene detection
      console.log('Scene detection completed:', data.scenes);
      return data.scenes;
    } else if (data.status === 'failed') {
      // Handle failure
      console.error('Scene detection failed:', data.error);
      throw new Error(data.error);
    } else {
      // Still processing, poll again after delay
      console.log(`Scene detection in progress: ${data.progress}%`);
      await new Promise(resolve => setTimeout(resolve, 2000));
      return pollSceneDetectionStatus(taskId);
    }
  } catch (error) {
    console.error('Failed to check scene detection status:', error);
    throw error;
  }
}
```

## Performance Considerations

1. **Resource Intensive**: Scene detection is computationally expensive, especially for high-resolution videos.

2. **Queue Management**: The implementation uses the existing TaskQueue with priority settings to ensure scene detection tasks don't block video rendering tasks.

3. **Optimizations**:
   - Thumbnail generation is performed only after scene detection is complete
   - Parallel processing is limited to avoid overloading the server
   - Temporary files are cleaned up after processing

4. **Configuration Options**:
   - `threshold`: Controls sensitivity of scene detection (0.1-1.0)
   - `minSceneDuration`: Filters out very short scenes to prevent over-segmentation

## Future Enhancements

1. **Cached Results**: Store detection results for previously processed videos
2. **Machine Learning**: Integrate more advanced ML-based scene detection for improved accuracy
3. **Preview Generation**: Create short preview clips for each detected scene
4. **Batch Processing**: Support detection across multiple videos simultaneously
5. **Advanced Filtering**: Additional filtering options (color changes, audio changes, etc.)